# LocalSEO - Australian Business SEO Analysis Platform

A comprehensive platform for analyzing and improving the local SEO presence of Australian businesses.

## Overview

LocalSEO is a Django-based web application that collects, analyzes, and provides recommendations for improving the local SEO presence of Australian businesses. The platform focuses on Google data sources to provide actionable insights.

## Features

- **Data Collection**: Gather business information from Google Places API
- **Website Analysis**: Analyze website performance using Google PageSpeed Insights API
- **Competitive Analysis**: Compare businesses against local competitors
- **Recommendation Engine**: Generate actionable SEO recommendations
- **Business Search**: Search and filter businesses by name, category, and location

## Technology Stack

- **Backend**: Python 3.x, Django 5.2
- **Database**: PostgreSQL
- **APIs**: Google Places API, Google PageSpeed Insights API

## Setup

### Prerequisites

- Python 3.x
- PostgreSQL
- API keys for Google Places and PageSpeed Insights

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd LocalSEO
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file in the project root with your API keys:
   ```
   GOOGLE_PLACES_API_KEY=your_google_places_api_key
   GOOGLE_PAGESPEED_API_KEY=your_pagespeed_api_key
   ```

5. Set up the database:
   ```
   python manage.py migrate
   ```

6. Create a superuser:
   ```
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```
   python manage.py runserver
   ```

## Usage

1. Log in to the platform using your superuser credentials
2. Use the "Collect Business Data" form to gather data from Google Places API
3. Search for businesses using the search form
4. View detailed business information, competitive analysis, and recommendations

## Project Structure

- `data_acquisition/`: Main Django app for data collection and analysis
  - `api_clients/`: API client implementations
  - `models.py`: Database models
  - `utils/`: Utility modules for data collection, analysis, and recommendations
  - `views.py`: View functions

## Future Development

- Integration with additional data sources
- Enhanced competitive analysis
- Advanced recommendation engine
- User management and role-based access
- Report generation and export

## License

[MIT License](LICENSE)
