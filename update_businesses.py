#!/usr/bin/env python
"""
<PERSON>ript to update all businesses in the database with details from Google Places API.
Run this script from the command line:
    python update_businesses.py
"""

import os
import sys
import django
import time
import requests
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LocalSEO.settings')
django.setup()

# Import Django models
from data_acquisition.models import Business
from data_acquisition.api_clients.google_places import GooglePlacesClient

# Your Google Places API key
API_KEY = "AIzaSyDWdtrf00HaJBwXD91Elu9s9k6IzTDVt64"

def update_business(business, client):
    """Update a business with details from Google Places API"""
    print(f"Updating business {business.id}: {business.name} (Place ID: {business.google_place_id})")
    
    try:
        # Get place details
        place_details = client.get_place_details(business.google_place_id)
        
        if not place_details:
            print(f"  No details found for Place ID: {business.google_place_id}")
            return False
        
        # Update business details
        business.name = place_details.get('name', business.name)
        business.phone = place_details.get('formatted_phone_number', business.phone)
        business.website = place_details.get('website', business.website)
        business.google_rating = place_details.get('rating', business.google_rating)
        business.google_reviews_count = place_details.get('user_ratings_total', business.google_reviews_count)
        
        # Update address
        if 'formatted_address' in place_details:
            address_parts = place_details['formatted_address'].split(',')
            
            # Australian addresses typically have format: "Street, Suburb STATE Postcode, Australia"
            if len(address_parts) >= 2:
                # First part is street address
                business.street_address = address_parts[0].strip()
                
                # Second part usually contains suburb, state and postcode
                location_part = address_parts[1].strip()
                location_parts = location_part.split()
                
                if len(location_parts) >= 2:
                    # Last part is usually the postcode
                    postcode_candidate = location_parts[-1]
                    if postcode_candidate.isdigit() and len(postcode_candidate) == 4:  # Australian postcodes are 4 digits
                        business.postcode = postcode_candidate
                        # Second last part is usually the state
                        if len(location_parts) >= 3:
                            business.state = location_parts[-2]
                            # Everything before state and postcode is the suburb/city
                            business.city = ' '.join(location_parts[:-2])
                    else:
                        # If no postcode, assume last part is state and rest is suburb
                        business.state = location_parts[-1]
                        business.city = ' '.join(location_parts[:-1])
        
        # Update coordinates
        if 'geometry' in place_details and 'location' in place_details['geometry']:
            location = place_details['geometry']['location']
            business.latitude = location.get('lat')
            business.longitude = location.get('lng')
        
        # Save the business
        business.save()
        
        print(f"  Updated: {business.name}, {business.street_address}, {business.city}, {business.state} {business.postcode}")
        print(f"  Website: {business.website}")
        
        # Add a delay to avoid hitting API rate limits
        time.sleep(0.5)
        
        return True
    
    except Exception as e:
        print(f"  Error updating business: {str(e)}")
        return False

def main():
    """Main function to update all businesses"""
    # Initialize Google Places client
    client = GooglePlacesClient(api_key=API_KEY)
    
    # Get all businesses with Google Place IDs
    businesses = Business.objects.filter(google_place_id__isnull=False).exclude(google_place_id='')
    
    print(f"Found {businesses.count()} businesses to update")
    
    # Update each business
    success_count = 0
    for business in businesses:
        if update_business(business, client):
            success_count += 1
    
    print(f"Updated {success_count} out of {businesses.count()} businesses")
    
    # Count businesses with .com.au domains
    com_au_count = Business.objects.filter(website__icontains='.com.au').count()
    print(f"Found {com_au_count} businesses with .com.au domains")

if __name__ == "__main__":
    main()
