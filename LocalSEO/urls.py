"""
URL configuration for LocalSEO project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.contrib.auth import views as auth_views
from data_acquisition.views import business as business_views
from data_acquisition.views import google_business_profile as gbp_views
from data_acquisition.views.oauth_redirect import oauth_redirect

urlpatterns = [
    path('admin/', admin.site.urls),

    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='data_acquisition/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),

    # Business data URLs
    path('', business_views.business_search, name='business_search'),
    path('business/<int:business_id>/', business_views.business_detail, name='business_detail'),
    path('collect/', business_views.collect_business_data, name='collect_business_data'),
    path('test-api-key/', business_views.test_api_key, name='test_api_key'),
    path('lookup-place/', business_views.lookup_place_details, name='lookup_place_details'),

    # Google Business Profile URLs
    path('gbp/connect/', gbp_views.gbp_connect, name='gbp_connect'),
    path('gbp/debug/', gbp_views.gbp_debug, name='gbp_debug'),  # Debug page for OAuth configuration
    path('gbp/callback/', gbp_views.gbp_oauth_callback, name='gbp_oauth_callback'),

    # OAuth redirect handlers - these will redirect to the gbp_oauth_callback view
    path('callback/', oauth_redirect, name='oauth_redirect1'),  # Alternative callback URL
    path('callback', oauth_redirect, name='oauth_redirect2'),  # Alternative callback URL without trailing slash
    path('oauth2callback/', oauth_redirect, name='oauth_redirect3'),  # Alternative callback URL for Google Cloud Console
    path('oauth2callback', oauth_redirect, name='oauth_redirect4'),  # Alternative callback URL without trailing slash

    # Dashboard and other views
    path('gbp/dashboard/', gbp_views.gbp_dashboard, name='gbp_dashboard'),
    path('gbp/refresh/<int:location_id>/', gbp_views.gbp_refresh_data, name='gbp_refresh_data'),
    path('gbp/competitors/<int:location_id>/', gbp_views.gbp_competitors, name='gbp_competitors'),
    path('gbp/recommendations/<int:location_id>/', gbp_views.gbp_generate_recommendations, name='gbp_generate_recommendations'),
]
