from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
import json
import os


class DataSource(models.Model):
    """Model to track different data sources"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    base_url = models.URLField(blank=True, null=True)
    api_key_required = models.BooleanField(default=False)
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Business(models.Model):
    """Main model for business data"""
    # Basic information
    name = models.Char<PERSON>ield(max_length=255)
    abn = models.CharField(max_length=20, blank=True, null=True)
    business_type = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)

    # Contact information
    phone = models.Char<PERSON><PERSON>(max_length=20, blank=True, null=True)
    email = models.Em<PERSON><PERSON>ield(blank=True, null=True)
    website = models.URLField(blank=True, null=True)

    # Address information
    street_address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    postcode = models.CharField(max_length=10, blank=True, null=True)
    country = models.CharField(max_length=50, default='Australia')
    latitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True)
    longitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True)

    # Google My Business information
    google_place_id = models.CharField(max_length=255, blank=True, null=True)
    google_rating = models.DecimalField(max_digits=3, decimal_places=1, blank=True, null=True)
    google_reviews_count = models.IntegerField(default=0)
    google_verified = models.BooleanField(default=False)

    # Google Business Profile metrics
    gbp_attributes_count = models.IntegerField(default=0)
    gbp_photos_count = models.IntegerField(default=0)
    gbp_posts_count = models.IntegerField(default=0)
    gbp_products_count = models.IntegerField(default=0)
    gbp_profile_completeness = models.IntegerField(default=0)
    gbp_services_count = models.IntegerField(default=0)
    gbp_questions_count = models.IntegerField(default=0)
    gbp_answers_count = models.IntegerField(default=0)
    gbp_bookings_count = models.IntegerField(default=0)
    gbp_menus_count = models.IntegerField(default=0)
    gbp_categories_count = models.IntegerField(default=0)
    gbp_special_hours_count = models.IntegerField(default=0)
    gbp_regular_hours_count = models.IntegerField(default=0)
    gbp_locations_count = models.IntegerField(default=0)

    # Social media
    facebook_url = models.URLField(blank=True, null=True)
    instagram_url = models.URLField(blank=True, null=True)
    linkedin_url = models.URLField(blank=True, null=True)

    # Website metrics
    page_speed_score = models.IntegerField(blank=True, null=True)
    mobile_friendly_score = models.IntegerField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_fetched = models.DateTimeField(auto_now=True)  # Changed from default=timezone.now to auto_now=True
    data_sources = models.ManyToManyField(DataSource, through='BusinessDataSource')

    def __str__(self):
        return self.name


class BusinessDataSource(models.Model):
    """Tracks which data sources were used for each business"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE)
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE)
    source_business_id = models.CharField(max_length=255, blank=True, null=True)
    last_fetched = models.DateTimeField(auto_now=True)  # Changed from default=timezone.now to auto_now=True
    data_quality_score = models.IntegerField(default=0)  # 0-100 score

    class Meta:
        unique_together = ('business', 'data_source')


class BusinessCategory(models.Model):
    """Categories for businesses"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='children')

    def __str__(self):
        return self.name


class BusinessCategoryMapping(models.Model):
    """Maps businesses to categories"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE)
    category = models.ForeignKey(BusinessCategory, on_delete=models.CASCADE)
    primary = models.BooleanField(default=False)

    class Meta:
        unique_together = ('business', 'category')


class BusinessHours(models.Model):
    """Operating hours for businesses"""
    DAYS_OF_WEEK = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]

    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='hours')
    day_of_week = models.IntegerField(choices=DAYS_OF_WEEK)
    open_time = models.TimeField()
    close_time = models.TimeField()
    is_closed = models.BooleanField(default=False)

    class Meta:
        unique_together = ('business', 'day_of_week')


class BusinessReview(models.Model):
    """Reviews for businesses"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='reviews')
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE)
    source_review_id = models.CharField(max_length=255, blank=True, null=True)
    reviewer_name = models.CharField(max_length=255, blank=True, null=True)
    rating = models.DecimalField(max_digits=3, decimal_places=1)
    review_text = models.TextField(blank=True, null=True)
    review_date = models.DateTimeField()

    class Meta:
        unique_together = ('business', 'data_source', 'source_review_id')


class BusinessKeyword(models.Model):
    """Keywords associated with businesses"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='keywords')
    keyword = models.CharField(max_length=100)
    source = models.ForeignKey(DataSource, on_delete=models.SET_NULL, blank=True, null=True)
    relevance_score = models.IntegerField(default=0)  # 0-100 score

    class Meta:
        unique_together = ('business', 'keyword')


class APIQuota(models.Model):
    """Tracks API usage and quotas"""
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE)
    daily_limit = models.IntegerField(default=0)
    daily_usage = models.IntegerField(default=0)
    monthly_limit = models.IntegerField(default=0)
    monthly_usage = models.IntegerField(default=0)
    reset_date = models.DateTimeField()

    def __str__(self):
        return f"{self.data_source.name} Quota"


class GoogleAccount(models.Model):
    """Google account information for a user"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='google_account')
    email = models.EmailField()
    name = models.CharField(max_length=255)
    picture_url = models.URLField(blank=True, null=True)
    access_token = models.TextField()
    refresh_token = models.TextField(blank=True, null=True)
    token_expiry = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.email} ({self.user.username})"

    def to_credentials_dict(self):
        """Convert to a dictionary for creating Credentials object"""
        return {
            'token': self.access_token,
            'refresh_token': self.refresh_token,
            'token_uri': 'https://oauth2.googleapis.com/token',
            'client_id': os.environ.get('GOOGLE_OAUTH_CLIENT_ID'),
            'client_secret': os.environ.get('GOOGLE_OAUTH_CLIENT_SECRET'),
            'scopes': [
                'openid',
                'https://www.googleapis.com/auth/business.manage',
                'https://www.googleapis.com/auth/userinfo.email',
                'https://www.googleapis.com/auth/userinfo.profile'
            ],
            'expiry': self.token_expiry.isoformat()
        }


class GoogleBusinessLocation(models.Model):
    """Google Business Profile location information"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='gbp_locations')
    google_account = models.ForeignKey(GoogleAccount, on_delete=models.CASCADE, related_name='locations')
    location_name = models.CharField(max_length=255)  # Resource name (e.g., accounts/123/locations/456)
    location_id = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    stored_location_type = models.CharField(max_length=50, blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    website_url = models.URLField(blank=True, null=True)
    profile_photo_url = models.URLField(blank=True, null=True)
    cover_photo_url = models.URLField(blank=True, null=True)
    attributes = models.TextField(blank=True, null=True)  # JSON field for storing attributes
    labels = models.TextField(blank=True, null=True)  # JSON field for storing labels
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_fetched = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    def get_attributes(self):
        """Get attributes as a dictionary"""
        if self.attributes:
            return json.loads(self.attributes)
        return {}

    def set_attributes(self, attributes_dict):
        """Set attributes from a dictionary"""
        self.attributes = json.dumps(attributes_dict)

    def get_labels(self):
        """Get labels as a list"""
        if self.labels:
            return json.loads(self.labels)
        return []

    def set_labels(self, labels_list):
        """Set labels from a list"""
        self.labels = json.dumps(labels_list)


class GoogleBusinessInsights(models.Model):
    """Google Business Profile insights/metrics"""
    location = models.ForeignKey(GoogleBusinessLocation, on_delete=models.CASCADE, related_name='insights')
    date = models.DateField()
    views_search = models.IntegerField(default=0)
    views_maps = models.IntegerField(default=0)
    queries_direct = models.IntegerField(default=0)
    queries_indirect = models.IntegerField(default=0)
    actions_website = models.IntegerField(default=0)
    actions_phone = models.IntegerField(default=0)
    actions_directions = models.IntegerField(default=0)
    photos_views = models.IntegerField(default=0)
    photos_count = models.IntegerField(default=0)
    local_post_views = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('location', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.location.title} - {self.date}"


class AIRecommendation(models.Model):
    """AI-generated recommendations for business improvement"""
    CATEGORY_CHOICES = [
        ('PROFILE', 'Profile Optimization'),
        ('REVIEWS', 'Review Strategy'),
        ('POSTS', 'Post Content'),
        ('HOURS', 'Business Hours'),
        ('PHOTOS', 'Photos'),
        ('WEBSITE', 'Website'),
        ('CATEGORIES', 'Categories'),
        ('ATTRIBUTES', 'Attributes'),
        ('GENERAL', 'General')
    ]

    IMPACT_CHOICES = [
        ('HIGH', 'High'),
        ('MEDIUM', 'Medium'),
        ('LOW', 'Low')
    ]

    EFFORT_CHOICES = [
        ('HIGH', 'High'),
        ('MEDIUM', 'Medium'),
        ('LOW', 'Low')
    ]

    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='ai_recommendations')
    title = models.CharField(max_length=255)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    impact = models.CharField(max_length=10, choices=IMPACT_CHOICES)
    effort = models.CharField(max_length=10, choices=EFFORT_CHOICES)
    action_url = models.URLField(blank=True, null=True)
    is_implemented = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title
