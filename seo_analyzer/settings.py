import os
from pathlib import Path
import environ

BASE_DIR = Path(__file__).resolve().parent.parent
env = environ.Env()
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))

SECRET_KEY = 'django-insecure-0bl*4@ptx(22!4i#+2zj%o#1@n6g-m+=3q%%%%7pi$xene(fc0'

DEBUG = True

ALLOWED_HOSTS = ['127.0.0.1', 'localhost', 'seoanalyser.com.au', 'www.seoanalyser.com.au', 'dev1.seoanalyser.com.au', 'dev2.seoanalyser.com.au', 'testserver']

if DEBUG:
    import socket
    hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
    ALLOWED_HOSTS += [ip for ip in ips] + [hostname]

    ALLOWED_HOSTS += [f'{host}:{port}' for host in ['seoanalyser.com.au', 'www.seoanalyser.com.au'] 
                    for port in ['2087']]

INSTALLED_APPS = [
    'whitenoise.runserver_nostatic',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'django.contrib.sitemaps',  # Add this line

    # Third-party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'taggit',  # Add this line
    'django_ckeditor_5',  # Replace 'ckeditor' and 'ckeditor_uploader'

    # Local apps
    'tools',
    'accounts',
    'session_manager',
    'blog.apps.BlogConfig',
    'contact',
]

# Add Taggit settings
TAGGIT_CASE_INSENSITIVE = True

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'accounts.auth_middleware.AuthenticationLoggingMiddleware',  # Add auth logging
    'accounts.auth_middleware.AuthenticationDebugMiddleware',    # Add auth debugging
    'tools.middleware.RequestLoggingMiddleware',  
    'tools.middleware.RateLimitMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',  # Required for allauth
    'accounts.middleware.AccountErrorHandlerMiddleware',
]

ROOT_URLCONF = 'seo_analyzer.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'seo_analyzer.wsgi.application'




DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env("DB_NAME"),
        'USER': env("DB_USER"),
        'PASSWORD': env("DB_PASSWORD"),
        'HOST': env("DB_HOST"),
        'PORT': env("DB_PORT"),
    }
}




AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'accounts.validators.ComplexPasswordValidator',
    },
]


LOGS_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

LOGGING_CONFIG = None

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'verbose': {
#             'format': '{asctime} {levelname} {module} {process:d} {thread:d} {message}',
#             'style': '{',
#         },
#         'detailed': {
#             'format': '{asctime} {levelname} {name} {pathname}:{lineno} {funcName} - {message}',
#             'style': '{',
#         },
#         'simple': {
#             'format': '{levelname} {message}',
#             'style': '{',
#         },
#         'request': {
#             'format': '{asctime} {levelname} REQUEST: {message}',
#             'style': '{',
#         }
#     },
#     'filters': {
#         'require_debug_true': {
#             '()': 'django.utils.log.RequireDebugTrue',
#         },
#         'require_debug_false': {
#             '()': 'django.utils.log.RequireDebugFalse',
#         },
#     },
#     'handlers': {
#         'console': {
#             'level': 'DEBUG',
#             'class': 'logging.StreamHandler',
#             'formatter': 'detailed',
#         },
#         'file': {
#             'level': 'INFO',
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(LOGS_DIR, 'django.log'),
#             'maxBytes': 10 * 1024 * 1024,  # 10 MB
#             'backupCount': 5,
#             'formatter': 'detailed',
#         },
#         'request_file': {
#             'level': 'DEBUG',
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(LOGS_DIR, 'requests.log'),
#             'maxBytes': 10 * 1024 * 1024,  # 10 MB
#             'backupCount': 5,
#             'formatter': 'request',
#         },
#         'error_file': {
#             'level': 'ERROR',
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(LOGS_DIR, 'django_error.log'),
#             'maxBytes': 10 * 1024 * 1024,  # 10 MB
#             'backupCount': 5,
#             'formatter': 'detailed',
#         },
#         'url_file': {
#             'level': 'DEBUG',
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(LOGS_DIR, 'url_resolution.log'),
#             'maxBytes': 10 * 1024 * 1024,  # 10 MB
#             'backupCount': 5,
#             'formatter': 'detailed',
#         },
#         'mail_admins': {
#             'level': 'ERROR',
#             'filters': ['require_debug_false'],
#             'class': 'django.utils.log.AdminEmailHandler',
#             'formatter': 'detailed',
#         },
#         'celery_file': {
#             'level': 'INFO',
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(LOGS_DIR, 'celery.log'),
#             'maxBytes': 10 * 1024 * 1024,  # 10 MB
#             'backupCount': 5,
#             'formatter': 'detailed',
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['console', 'file', 'mail_admins'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#         'django.server': {
#             'handlers': ['console', 'file'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#         'django.request': {
#             'handlers': ['error_file', 'console', 'mail_admins'],
#             'level': 'DEBUG',  # Capture all request-related messages
#             'propagate': False,
#         },
#         'django.db.backends': {
#             'handlers': ['console', 'file'],
#             'level': 'DEBUG' if DEBUG else 'INFO',
#             'propagate': False,
#         },
#         'django.security': {
#             'handlers': ['error_file', 'console', 'mail_admins'],
#             'level': 'ERROR',
#             'propagate': False,
#         },
#         'django.template': {
#             'handlers': ['console', 'file'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#         'django.utils.autoreload': {
#             'handlers': ['console'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#         'celery': {
#             'handlers': ['console', 'celery_file'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#         'tools.middleware': {  # Logger for your custom middleware
#             'handlers': ['request_file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'tools.signals': {  # Logger for your signals
#             'handlers': ['file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'tools.scraper': {  # Logger for your scraper module
#             'handlers': ['file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'tools.seo_api': {  # Logger for your seo_api module
#             'handlers': ['file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'tools.tasks': {  # Logger for your tasks module
#             'handlers': ['celery_file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'your_app_name': {  # Replace 'your_app_name' with your app's name
#             'handlers': ['console', 'file'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'url_resolver': {
#             'handlers': ['url_file', 'console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#         'playwright': {
#             'handlers': ['console'],
#             'level': 'WARNING',  # Set to WARNING to reduce verbosity
#             'propagate': False,
#         },
#         # Add other loggers here as needed
#     }
# }

# Email settings for error reporting
# ADMINS = [('Your Name', '<EMAIL>')]
# MANAGERS = ADMINS
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = env('EMAIL_HOST')
# EMAIL_PORT = env('EMAIL_PORT')
# EMAIL_USE_TLS = env('EMAIL_USE_TLS')
# EMAIL_HOST_USER = env('EMAIL_HOST_USER')
# EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')
# SERVER_EMAIL = env('SERVER_EMAIL')  # Email address for automated error reports
# DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL')  # Default email for other automated messages


LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


STATIC_URL = '/static/'  
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')


STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]


STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'


MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')




DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'


CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_TASK_RETRY_ON_FAILURE = True


CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}


REDIS_MAX_CONNECTIONS = 20
REDIS_SOCKET_TIMEOUT = 5


REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0


REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',  # Add this for browsable API
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/day',
        'user': '1000/day',
    },
}

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOWED_ORIGINS = [
    "https://seoanalyser.com.au",
    "https://www.seoanalyser.com.au",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3001",
    "http://testserver"
]

# Cookie settings
SESSION_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax'

# CSRF Trusted Origins
CSRF_TRUSTED_ORIGINS = [
    "https://seoanalyser.com.au",
    "https://dev1.seoanalyser.com.au",
    "https://dev2.seoanalyser.com.au",
    "http://testserver"
    # Add other trusted origins if needed, e.g., http://localhost:xxxx
]

# Custom User Model
AUTH_USER_MODEL = 'accounts.CustomUser'

# Simple JWT settings
from datetime import timedelta

SIMPLEJWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'REMEMBER_ME_ACCESS_TOKEN_LIFETIME': timedelta(days=7),
    'REMEMBER_ME_REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'BLACKLIST_ON_LOGOUT': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=5),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}

# Google OAuth2 settings
GOOGLE_OAUTH2_CLIENT_ID = '************-oodr3rhtpnanig5iba4ac8fkhkaom6e5.apps.googleusercontent.com'
GOOGLE_OAUTH2_CLIENT_SECRET = '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'
GOOGLE_OAUTH2_REDIRECT_URI = 'https://seoanalyser.com.au/api/accounts/google/callback/'

# Site framework settings
SITE_ID = 1

# Set the default site domain and name
# This will be used for email verification links
SITE_DOMAIN = os.environ.get('SITE_DOMAIN', 'seoanalyser.com.au')
SITE_NAME = os.environ.get('SITE_NAME', 'SEO Analyser')

# Update site in database on startup
from django.db import connection
from django.db.utils import ProgrammingError

def update_site_domain():
    try:
        from django.contrib.sites.models import Site
        site = Site.objects.get(id=SITE_ID)
        site.domain = SITE_DOMAIN
        site.name = SITE_NAME
        site.save()
    except ImportError:
        pass
    except (ProgrammingError, NameError):
        pass
    except Exception:
        # Catch any other exceptions that might occur
        pass

# Only run this if not in a management command
import sys
if 'runserver' in sys.argv:
    update_site_domain()

# Django Allauth settings
ACCOUNT_LOGIN_METHODS = {'email'}
ACCOUNT_SIGNUP_FIELDS = ['email*', 'password1*', 'password2*']
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'

# Social account settings
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': GOOGLE_OAUTH2_CLIENT_ID,
            'secret': GOOGLE_OAUTH2_CLIENT_SECRET,
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        }
    }
}

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', 'shvo hgpr hdgu zhyj')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Google reCAPTCHA v3 settings
RECAPTCHA_SITE_KEY = '6Le6WyErAAAAAEPOo_D3koUAjHiHrPwvoQICxk9J'
RECAPTCHA_SECRET_KEY = '6Le6WyErAAAAAB4kX-cfnG5I_--FHtnei6qWwgD6'
RECAPTCHA_REQUIRED_SCORE = 0.5
CONTACT_EMAIL = env('CONTACT_EMAIL', default='<EMAIL>')
PROXY_STRING = "geo.iproyal.com:12321:KKVZXvfpSE9zOGmY:pUgT1YPFo6skrA1a_country-au_streaming-1"

# DataForSEO API Credentials
DATAFORSEO_API_LOGIN = os.environ.get('DATAFORSEO_API_LOGIN', None)
DATAFORSEO_API_PASSWORD = os.environ.get('DATAFORSEO_API_PASSWORD', None)

STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY', 'sk_test_51RNDQH4WDmmz94GBnXGjqnPBM4VsgIp1dlPnrczF5T73oClc9Z1bmKOK79K4wxog336a2xQdOjnjn9uvz6qq7XIY00OMyXmAd4')
STRIPE_PUBLIC_KEY = os.environ.get('STRIPE_PUBLIC_KEY', 'pk_test_51RNDQH4WDmmz94GBJEhyInpmMq0sjWnErgmmOwK242aeTOqfkIQpojuykAsPgp7EO4UwHcf5HfH5vv0VpMhJvGml00nKJZHn95')
STRIPE_WEBHOOK_SECRET = os.environ.get('STRIPE_WEBHOOK_SECRET', 'whsec_hekhIzNd0CWTxLEJ6i50sDXWM6UwzLwW')

CKEDITOR_5_CONFIGS = {
    'default': {
        'toolbar': ['heading', '|', 'bold', 'italic', 'link',
                   'bulletedList', 'numberedList', 'blockQuote', 'imageUpload', '|',
                   'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                   'imageTextAlternative', 'imageStyle:full', 'imageStyle:side', '|',
                   'mediaEmbed', 'insertTable', '|', 'undo', 'redo'],
        'image': {
            'toolbar': ['imageTextAlternative', '|', 'imageStyle:alignLeft',
                       'imageStyle:full', 'imageStyle:alignRight'],
            'styles': [
                'full',
                'side',
                'alignLeft',
                'alignRight',
            ],
        },
        'height': '300px',
    }
}

CKEDITOR_5_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
CKEDITOR_5_UPLOAD_PATH = "uploads/"
CKEDITOR_5_ENDPOINT_URL = "/api/upload/image_upload/"  # Make sure this matches your URL configuration

# Make sure media files are properly served in development
if DEBUG:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Elasticsearch configuration
INSTALLED_APPS += [
    'django_elasticsearch_dsl',
]

ELASTICSEARCH_DSL = {
    'default': {
        'hosts': 'http://localhost:9200'
    },
}

# Name of the Elasticsearch index
ELASTICSEARCH_INDEX_NAMES = {
    'blog.documents.PostDocument': 'posts',
}

SITE_URL = 'https://seoanalyser.com.au'
FRONTEND_CHECKOUT_URL_BASE = 'https://seoanalyser.com.au/checkout'
FRONTEND_DOMAIN_BASE = 'https://seoanalyser.com.au'